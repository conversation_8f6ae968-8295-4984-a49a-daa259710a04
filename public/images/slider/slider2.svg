<svg width="1200" height="600" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="1200" height="600" fill="url(#grad2)"/>
  <polygon points="950,100 1000,150 950,200 900,150" fill="rgba(255,255,255,0.1)"/>
  <polygon points="1050,250 1100,300 1050,350 1000,300" fill="rgba(255,255,255,0.08)"/>
  <polygon points="850,350 900,400 850,450 800,400" fill="rgba(255,255,255,0.06)"/>
  <text x="100" y="250" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">ISO Certification</text>
  <text x="100" y="300" fill="rgba(255,255,255,0.9)" font-family="Arial, sans-serif" font-size="32">Trusted Authority</text>
  <text x="100" y="350" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="18">Professional certification services</text>
</svg>
