<svg width="1200" height="600" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="1200" height="600" fill="url(#grad1)"/>
  <circle cx="900" cy="150" r="80" fill="rgba(255,255,255,0.1)"/>
  <circle cx="1050" cy="300" r="60" fill="rgba(255,255,255,0.05)"/>
  <circle cx="800" cy="450" r="100" fill="rgba(255,255,255,0.08)"/>
  <text x="100" y="250" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">Quality Assurance</text>
  <text x="100" y="300" fill="rgba(255,255,255,0.9)" font-family="Arial, sans-serif" font-size="32">Excellence in Certification</text>
  <text x="100" y="350" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="18">Safeguarding life, property and environment</text>
</svg>
