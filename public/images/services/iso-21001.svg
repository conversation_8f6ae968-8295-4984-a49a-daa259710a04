<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="iso21001" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fed7aa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ea580c;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#iso21001)"/>
  <circle cx="300" cy="80" r="35" fill="rgba(255,255,255,0.3)"/>
  <circle cx="340" cy="190" r="20" fill="rgba(255,255,255,0.2)"/>
  <text x="50" y="120" fill="white" font-family="Arial, sans-serif" font-size="36" font-weight="bold">ISO</text>
  <text x="50" y="160" fill="white" font-family="Arial, sans-serif" font-size="36" font-weight="bold">21001:2018</text>
  <text x="50" y="200" fill="rgba(255,255,255,0.9)" font-family="Arial, sans-serif" font-size="16">Educational</text>
  <text x="50" y="220" fill="rgba(255,255,255,0.9)" font-family="Arial, sans-serif" font-size="16">Organizations</text>
</svg>
