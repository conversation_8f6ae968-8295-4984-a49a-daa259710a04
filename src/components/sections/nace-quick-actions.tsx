"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Award, Shield } from "lucide-react";
import Link from "next/link";

const quickActions = [
  {
    icon: <FileText className="h-12 w-12 text-blue-600" />,
    title: "Apply Online",
    description: "Apply Now to explore our Courses.",
    href: "/contact-us",
    buttonText: "Apply Now",
    bgColor: "bg-blue-50",
  },
  {
    icon: <Award className="h-12 w-12 text-green-600" />,
    title: "Accredited By",
    description: "United Accreditation Foundation",
    href: "/accreditation",
    buttonText: "Learn More",
    bgColor: "bg-green-50",
  },
  {
    icon: <Shield className="h-12 w-12 text-purple-600" />,
    title: "VERIFY CERTIFICATE",
    description: "Just one click to Verify your Certificate here.",
    href: "/client",
    buttonText: "Verify Now",
    bgColor: "bg-purple-50",
  },
];

export default function NaceQuickActions() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {quickActions.map((action, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md"
            >
              <CardContent className="p-8 text-center">
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${action.bgColor} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {action.icon}
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {action.title}
                </h3>
                
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {action.description}
                </p>
                
                <Link href={action.href}>
                  <Button 
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3"
                    size="lg"
                  >
                    {action.buttonText}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
