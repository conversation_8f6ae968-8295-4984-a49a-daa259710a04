"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Shield, Lock, Cog, GraduationCap } from "lucide-react";
import Link from "next/link";

const services = [
  {
    id: 1,
    title: "ISO 9001:2015 CERTIFICATION",
    description: "Quality Management System certification that helps organizations ensure they meet customer and regulatory requirements while continuously improving their processes.",
    icon: <Shield className="h-8 w-8 text-blue-600" />,
    image: "/images/services/iso-9001.svg",
    features: ["Quality Management", "Process Improvement", "Customer Satisfaction", "Regulatory Compliance"],
    href: "/iso-9001-2015",
    color: "blue",
  },
  {
    id: 2,
    title: "ISO/IEC 27001:2022 CERTIFICATION",
    description: "Information Security Management System certification that helps organizations protect their information assets and manage security risks effectively.",
    icon: <Lock className="h-8 w-8 text-green-600" />,
    image: "/images/services/iso-27001.svg",
    features: ["Information Security", "Risk Management", "Data Protection", "Cyber Security"],
    href: "/iso-27001-2022",
    color: "green",
  },
  {
    id: 3,
    title: "ISO/IEC 20000-1:2018",
    description: "IT Service Management certification that helps organizations deliver high-quality IT services and improve service management processes.",
    icon: <Cog className="h-8 w-8 text-purple-600" />,
    image: "/images/services/iso-20000.svg",
    features: ["IT Service Management", "Service Delivery", "Process Optimization", "Customer Focus"],
    href: "/iso-20000-1-2018",
    color: "purple",
  },
  {
    id: 4,
    title: "ISO 21001:2018",
    description: "Educational Organizations Management System certification designed specifically for educational institutions to enhance learning outcomes.",
    icon: <GraduationCap className="h-8 w-8 text-orange-600" />,
    image: "/images/services/iso-21001.svg",
    features: ["Educational Excellence", "Learning Outcomes", "Stakeholder Satisfaction", "Continuous Improvement"],
    href: "/iso-21001-2018",
    color: "orange",
  },
];

const colorClasses = {
  blue: {
    bg: "bg-blue-50",
    border: "border-blue-200",
    text: "text-blue-600",
    button: "bg-blue-600 hover:bg-blue-700",
    badge: "bg-blue-100 text-blue-800",
  },
  green: {
    bg: "bg-green-50",
    border: "border-green-200",
    text: "text-green-600",
    button: "bg-green-600 hover:bg-green-700",
    badge: "bg-green-100 text-green-800",
  },
  purple: {
    bg: "bg-purple-50",
    border: "border-purple-200",
    text: "text-purple-600",
    button: "bg-purple-600 hover:bg-purple-700",
    badge: "bg-purple-100 text-purple-800",
  },
  orange: {
    bg: "bg-orange-50",
    border: "border-orange-200",
    text: "text-orange-600",
    button: "bg-orange-600 hover:bg-orange-700",
    badge: "bg-orange-100 text-orange-800",
  },
};

export default function NaceServices() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Our Services
          </h2>
          <div className="w-20 h-1 bg-blue-600 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We provide comprehensive ISO certification services to help your organization
            achieve excellence in quality, security, and management systems.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {services.map((service) => {
            const colors = colorClasses[service.color as keyof typeof colorClasses];

            return (
              <Card
                key={service.id}
                className={`group hover:shadow-xl transition-all duration-300 border-2 ${colors.border} overflow-hidden`}
              >
                {/* Service Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      // Fallback to colored background if image fails
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  <div className={`hidden w-full h-full ${colors.bg} flex items-center justify-center`}>
                    {service.icon}
                  </div>

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300"></div>

                  {/* Icon overlay */}
                  <div className={`absolute top-4 right-4 p-3 ${colors.bg} rounded-full shadow-lg`}>
                    {service.icon}
                  </div>
                </div>

                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    {service.title}
                  </CardTitle>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-gray-600 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features */}
                  <div className="flex flex-wrap gap-2">
                    {service.features.map((feature, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className={`${colors.badge} text-xs`}
                      >
                        {feature}
                      </Badge>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <Link href={service.href} className="block pt-4">
                    <Button
                      className={`w-full ${colors.button} text-white font-semibold group-hover:shadow-lg transition-all duration-300`}
                      size="lg"
                    >
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-gray-600 mb-6">
            Need help choosing the right certification for your organization?
          </p>
          <Button
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8"
          >
            Contact Our Experts
          </Button>
        </div>
      </div>
    </section>
  );
}
