"use client";

import { Icons } from "@/components/icons";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { MapPin, Phone, Mail, ExternalLink } from "lucide-react";
import Link from "next/link";

export default function NaceFooter() {
  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-6">
            <div>
              <img
                src="/logo/nace-logo-white.svg"
                alt="NACE Certification"
                className="h-12 w-auto mb-4"
              />
            </div>

            <p className="text-gray-300 leading-relaxed">
              NACE Certification Pvt. Ltd. is established as a certification body and its main objective to
              safeguard life, property and environment through quality assurance and total quality management.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-blue-400 mt-1 flex-shrink-0" />
                <span className="text-gray-300">
                  6C, Block-2, Sector-135 Noida-201304
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-blue-400 flex-shrink-0" />
                <span className="text-gray-300">+91-120-3170247</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-blue-400 flex-shrink-0" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* About Us Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">About Us</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/introduction" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Introduction
                </Link>
              </li>
              <li>
                <Link href="/certification-decision-and-audit-process" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Certification Decision and Audit Process
                </Link>
              </li>
              <li>
                <Link href="/complaint-appeal-process" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Complaint & Appeal Process
                </Link>
              </li>
              <li>
                <Link href="/impartiality-policy" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Impartiality Policy
                </Link>
              </li>
              <li>
                <Link href="/rules-and-regulations" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Rules And Regulations
                </Link>
              </li>
              <li>
                <Link href="/accredited-management-system-process" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Accredited Management System Process
                </Link>
              </li>
            </ul>
          </div>

          {/* ISO Certification Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">ISO Certification</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/iso-9001-2015" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  ISO 9001:2015 Certification
                </Link>
              </li>
              <li>
                <Link href="/iso-27001-2022" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  ISO/IEC 27001:2022 Certification
                </Link>
              </li>
              <li>
                <Link href="/iso-20000-1-2018" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  ISO/IEC 20000-1:2018
                </Link>
              </li>
              <li>
                <Link href="/iso-21001-2018" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  ISO 21001:2018
                </Link>
              </li>
            </ul>

            <h4 className="text-lg font-semibold mb-4 mt-8">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/accreditation" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Accreditation
                </Link>
              </li>
              <li>
                <Link href="/certification-process" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Certification Process
                </Link>
              </li>
              <li>
                <Link href="/client" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Client
                </Link>
              </li>
              <li>
                <Link href="/download" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Download
                </Link>
              </li>
              <li>
                <Link href="/contact-us" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter & Social */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Stay Connected</h4>

            {/* Newsletter Signup */}
            <div className="mb-8">
              <p className="text-gray-300 mb-4">
                Subscribe to our newsletter for updates on certification processes and industry news.
              </p>
              <div className="flex space-x-2">
                <Input
                  type="email"
                  placeholder="Your email address"
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                />
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  Subscribe
                </Button>
              </div>
            </div>

            {/* Social Media Links */}
            <div>
              <h5 className="font-medium mb-4">Follow Us</h5>
              <div className="flex space-x-4">
                <Link
                  href="#"
                  className="bg-gray-800 p-3 rounded-full hover:bg-blue-600 transition-colors duration-200"
                  aria-label="Facebook"
                >
                  <Icons.facebook className="h-5 w-5" />
                </Link>
                <Link
                  href="#"
                  className="bg-gray-800 p-3 rounded-full hover:bg-pink-600 transition-colors duration-200"
                  aria-label="Instagram"
                >
                  <Icons.instagram className="h-5 w-5" />
                </Link>
                <Link
                  href="#"
                  className="bg-gray-800 p-3 rounded-full hover:bg-blue-500 transition-colors duration-200"
                  aria-label="LinkedIn"
                >
                  <Icons.linkedin className="h-5 w-5" />
                </Link>
                <Link
                  href="#"
                  className="bg-gray-800 p-3 rounded-full hover:bg-blue-400 transition-colors duration-200"
                  aria-label="Twitter"
                >
                  <Icons.twitter className="h-5 w-5" />
                </Link>
              </div>
            </div>

            {/* WhatsApp Contact */}
            <div className="mt-6 p-4 bg-green-600 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="bg-white p-2 rounded-full">
                  <Phone className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <div className="font-medium">WhatsApp Support</div>
                  <div className="text-sm text-green-100">+91-8285008681</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator className="bg-gray-800" />

      {/* Bottom Footer */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="text-gray-400 text-sm">
            © 2024 NACE Certification Pvt. Ltd. All rights reserved.
          </div>
          <div className="flex space-x-6 text-sm">
            <Link href="/privacy-policy" className="text-gray-400 hover:text-blue-400 transition-colors duration-200">
              Privacy Policy
            </Link>
            <Link href="/terms-of-service" className="text-gray-400 hover:text-blue-400 transition-colors duration-200">
              Terms of Service
            </Link>
            <Link href="/sitemap" className="text-gray-400 hover:text-blue-400 transition-colors duration-200">
              Sitemap
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
