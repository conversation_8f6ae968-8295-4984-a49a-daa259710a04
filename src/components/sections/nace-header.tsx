"use client";

import { Icons } from "@/components/icons";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useState } from "react";
import { Menu, X } from "lucide-react";

export default function NaceHeader() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="sticky top-0 z-50 bg-white border-b border-gray-200">
      {/* Top bar with contact info */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-2 text-sm">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Icons.mail className="h-4 w-4 text-gray-600" />
                <span>Email <EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <Icons.phone className="h-4 w-4 text-gray-600" />
                <span>Call Now +91-120-3170247</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/client" className="hover:text-blue-600">Client</Link>
              <Link href="/register" className="hover:text-blue-600">Register</Link>
              <div className="flex gap-2">
                <Link href="#" className="hover:opacity-80">
                  <Icons.facebook className="h-4 w-4" />
                </Link>
                <Link href="#" className="hover:opacity-80">
                  <Icons.instagram className="h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main navigation */}
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <img
              src="/logo/nace-logo.svg"
              alt="NACE Certification Pvt. Ltd."
              className="h-12 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:block">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <Link href="/" legacyBehavior passHref>
                    <NavigationMenuLink className="px-4 py-2 hover:text-blue-600">
                      Home
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger className="px-4 py-2">About Us</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="w-64 p-4">
                      <div className="space-y-2">
                        <Link href="/introduction" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          Introduction
                        </Link>
                        <Link href="/certification-decision-and-audit-process" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          Certification Decision and Audit Process
                        </Link>
                        <Link href="/complaint-appeal-process" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          Complaint & Appeal Process
                        </Link>
                        <Link href="/impartiality-policy" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          Impartiality Policy
                        </Link>
                        <Link href="/rules-and-regulations" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          Rules And Regulations for Use of Certification & Accreditation Mark
                        </Link>
                        <Link href="/accredited-management-system-process" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          Accredited Management System Process
                        </Link>
                      </div>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger className="px-4 py-2">ISO Certification</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="w-64 p-4">
                      <div className="space-y-2">
                        <Link href="/iso-9001-2015" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          ISO 9001:2015 Certification
                        </Link>
                        <Link href="/iso-27001-2022" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          ISO/IEC 27001:2022 Certification
                        </Link>
                        <Link href="/iso-20000-1-2018" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          ISO/IEC 20000-1:2018
                        </Link>
                        <Link href="/iso-21001-2018" className="block px-3 py-2 hover:bg-gray-100 rounded">
                          ISO 21001:2018
                        </Link>
                      </div>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <Link href="/accreditation" legacyBehavior passHref>
                    <NavigationMenuLink className="px-4 py-2 hover:text-blue-600">
                      Accreditation
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <Link href="/certification-process" legacyBehavior passHref>
                    <NavigationMenuLink className="px-4 py-2 hover:text-blue-600">
                      Certification Process
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <Link href="/client" legacyBehavior passHref>
                    <NavigationMenuLink className="px-4 py-2 hover:text-blue-600">
                      Client
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <Link href="/download" legacyBehavior passHref>
                    <NavigationMenuLink className="px-4 py-2 hover:text-blue-600">
                      Download
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <Link href="/contact-us" legacyBehavior passHref>
                    <NavigationMenuLink className="px-4 py-2 hover:text-blue-600">
                      Contact Us
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </nav>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 py-4">
            <div className="space-y-2">
              <Link href="/" className="block px-4 py-2 hover:bg-gray-100">Home</Link>
              <div className="px-4 py-2">
                <div className="font-medium text-gray-900 mb-2">About Us</div>
                <div className="pl-4 space-y-1">
                  <Link href="/introduction" className="block py-1 text-sm hover:text-blue-600">Introduction</Link>
                  <Link href="/certification-decision-and-audit-process" className="block py-1 text-sm hover:text-blue-600">Certification Decision and Audit Process</Link>
                  <Link href="/complaint-appeal-process" className="block py-1 text-sm hover:text-blue-600">Complaint & Appeal Process</Link>
                  <Link href="/impartiality-policy" className="block py-1 text-sm hover:text-blue-600">Impartiality Policy</Link>
                </div>
              </div>
              <div className="px-4 py-2">
                <div className="font-medium text-gray-900 mb-2">ISO Certification</div>
                <div className="pl-4 space-y-1">
                  <Link href="/iso-9001-2015" className="block py-1 text-sm hover:text-blue-600">ISO 9001:2015 Certification</Link>
                  <Link href="/iso-27001-2022" className="block py-1 text-sm hover:text-blue-600">ISO/IEC 27001:2022 Certification</Link>
                  <Link href="/iso-20000-1-2018" className="block py-1 text-sm hover:text-blue-600">ISO/IEC 20000-1:2018</Link>
                  <Link href="/iso-21001-2018" className="block py-1 text-sm hover:text-blue-600">ISO 21001:2018</Link>
                </div>
              </div>
              <Link href="/accreditation" className="block px-4 py-2 hover:bg-gray-100">Accreditation</Link>
              <Link href="/certification-process" className="block px-4 py-2 hover:bg-gray-100">Certification Process</Link>
              <Link href="/client" className="block px-4 py-2 hover:bg-gray-100">Client</Link>
              <Link href="/download" className="block px-4 py-2 hover:bg-gray-100">Download</Link>
              <Link href="/contact-us" className="block px-4 py-2 hover:bg-gray-100">Contact Us</Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
