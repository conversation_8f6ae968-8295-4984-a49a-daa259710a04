"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Shield, Scale, TrendingUp, Award, Users, Headphones } from "lucide-react";

const features = [
  {
    icon: <Shield className="h-8 w-8 text-blue-600" />,
    title: "Commitment to Safety & Quality",
    description: "We are dedicated to safeguarding life, property, and the environment through stringent quality assurance and total quality management.",
    color: "blue",
  },
  {
    icon: <Scale className="h-8 w-8 text-green-600" />,
    title: "Impartial & Transparent Certification",
    description: "Our certification processes are conducted without bias, discrimination, or prejudice—ensuring fair and credible results every time.",
    color: "green",
  },
  {
    icon: <TrendingUp className="h-8 w-8 text-purple-600" />,
    title: "Continuous Improvement Culture",
    description: "We foster a culture of continuous improvement, where every employee actively contributes to enhancing our processes and services.",
    color: "purple",
  },
  {
    icon: <Award className="h-8 w-8 text-orange-600" />,
    title: "Trusted Certification Body",
    description: "As an established and recognized certification authority, we offer reliable recognition of management systems across industries.",
    color: "orange",
  },
  {
    icon: <Users className="h-8 w-8 text-red-600" />,
    title: "Experienced and Ethical Auditors",
    description: "Our auditors are skilled, ethical, and committed to upholding the highest standards of integrity and competence.",
    color: "red",
  },
  {
    icon: <Headphones className="h-8 w-8 text-indigo-600" />,
    title: "Strong Support System",
    description: "We ensure seamless coordination and support throughout your certification journey—from application to audit to post-certification service.",
    color: "indigo",
  },
];

const colorClasses = {
  blue: "bg-blue-50 border-blue-200",
  green: "bg-green-50 border-green-200",
  purple: "bg-purple-50 border-purple-200",
  orange: "bg-orange-50 border-orange-200",
  red: "bg-red-50 border-red-200",
  indigo: "bg-indigo-50 border-indigo-200",
};

export default function NaceWhyChooseUs() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Why Choose Us
          </h2>
          <div className="w-20 h-1 bg-blue-600 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover what sets NACE Certification apart as your trusted partner 
            in achieving excellence and compliance.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index}
              className={`group hover:shadow-lg transition-all duration-300 border-2 ${
                colorClasses[feature.color as keyof typeof colorClasses]
              } hover:scale-105`}
            >
              <CardContent className="p-8 text-center">
                {/* Icon */}
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-white shadow-md mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                
                {/* Title */}
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                  {feature.title}
                </h3>
                
                {/* Description */}
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom Stats Section */}
        <div className="mt-20 bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Our Track Record</h3>
            <p className="text-blue-100 text-lg">
              Numbers that speak for our commitment to excellence
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-blue-200">Companies Certified</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">15+</div>
              <div className="text-blue-200">Years of Experience</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-blue-200">Expert Auditors</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">99%</div>
              <div className="text-blue-200">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
