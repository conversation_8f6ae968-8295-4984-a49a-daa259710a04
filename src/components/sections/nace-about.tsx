"use client";

import { Button } from "@/components/ui/button";
import Image from "next/image";

export default function NaceAbout() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h2 className="text-4xl font-bold text-gray-900 leading-tight">
                <span className="text-blue-600">NACE Certification Pvt. Ltd.</span>
              </h2>
              <div className="w-20 h-1 bg-blue-600"></div>
            </div>

            <div className="space-y-4 text-gray-700 leading-relaxed">
              <p className="text-lg">
                NACE Certification Pvt. Ltd. is established as a certification body and its main objective to
                <strong className="text-gray-900"> safeguard life, property and environment</strong> through
                quality assurance and total quality management.
              </p>

              <p>
                NACE shall create an environment where each employee contributes to all aspects of our business
                process and shall strive for continuous improvement to meet with customer Satisfaction by having
                a strong feedback system from clients, auditors and certification staff.
              </p>

              <p>
                We offer <strong className="text-gray-900">certification and recognition of management systems</strong>,
                without bias, discrimination or prejudice.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8"
              >
                Learn More About Us
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8"
              >
                Our Services
              </Button>
            </div>

            {/* Key Stats */}
            <div className="grid grid-cols-2 gap-6 pt-8">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-3xl font-bold text-blue-600 mb-2">500+</div>
                <div className="text-sm text-gray-600">Certified Companies</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-3xl font-bold text-green-600 mb-2">15+</div>
                <div className="text-sm text-gray-600">Years Experience</div>
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="relative">
            <div className="relative overflow-hidden rounded-2xl shadow-2xl">
              <img
                src="/images/about/nace-office.svg"
                alt="NACE Certification Office"
                className="w-full h-[500px] object-cover"
              />

              {/* Overlay with company highlights */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
                <div className="absolute bottom-6 left-6 right-6 text-white">
                  <h3 className="text-xl font-semibold mb-2">Quality Assurance Excellence</h3>
                  <p className="text-sm opacity-90">
                    Committed to maintaining the highest standards in certification processes
                  </p>
                </div>
              </div>
            </div>

            {/* Floating certification badge */}
            <div className="absolute -top-6 -right-6 bg-white rounded-full p-6 shadow-xl border-4 border-blue-100">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">ISO</div>
                <div className="text-xs text-gray-600">Certified</div>
              </div>
            </div>

            {/* Background decoration */}
            <div className="absolute -bottom-6 -left-6 w-24 h-24 bg-blue-100 rounded-full opacity-50"></div>
            <div className="absolute -top-12 -left-12 w-32 h-32 bg-green-100 rounded-full opacity-30"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
