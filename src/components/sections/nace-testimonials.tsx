"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Quote } from "lucide-react";
import { useState, useEffect } from "react";

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON>",
    position: "Quality Engineer",
    company: "Hydrocarbon Industries",
    content: "The NACE training was incredibly detailed and practical. The instructors had real-world experience, and I felt fully prepared for the certification exam. Highly recommend to anyone in the coatings or corrosion field!",
    rating: 5,
    avatar: "/images/testimonials/rakesh.jpg",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    position: "IT Security Manager",
    company: "TechCorp Solutions",
    content: "NACE's ISO 27001 certification process was smooth and professional. Their auditors were knowledgeable and provided valuable insights that helped us improve our information security management system significantly.",
    rating: 5,
    avatar: "/images/testimonials/priya.jpg",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    position: "Operations Director",
    company: "Manufacturing Excellence Ltd",
    content: "We achieved ISO 9001:2015 certification with NACE's guidance. The entire process was transparent, and their team was always available to answer our questions. Our quality management has improved tremendously.",
    rating: 5,
    avatar: "/images/testimonials/amit.jpg",
  },
  {
    id: 4,
    name: "Dr. Sarah Johnson",
    position: "Academic Director",
    company: "Global Education Institute",
    content: "NACE helped us implement ISO 21001:2018 for our educational organization. The certification has enhanced our credibility and improved our educational processes. Excellent service and support throughout.",
    rating: 5,
    avatar: "/images/testimonials/sarah.jpg",
  },
];

export default function NaceTestimonials() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 6000);

    return () => clearInterval(timer);
  }, []);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-5 w-5 ${
          index < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Testimonials
          </h2>
          <div className="w-20 h-1 bg-blue-600 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Hear what our clients say about their certification experience with NACE
          </p>
        </div>

        {/* Featured Testimonial */}
        <div className="max-w-4xl mx-auto mb-16">
          <Card className="border-0 shadow-xl bg-white">
            <CardContent className="p-12">
              <div className="text-center">
                {/* Quote Icon */}
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-8">
                  <Quote className="h-8 w-8 text-blue-600" />
                </div>

                {/* Testimonial Content */}
                <blockquote className="text-2xl font-medium text-gray-900 mb-8 leading-relaxed">
                  "{testimonials[currentTestimonial].content}"
                </blockquote>

                {/* Rating */}
                <div className="flex justify-center mb-6">
                  {renderStars(testimonials[currentTestimonial].rating)}
                </div>

                {/* Author */}
                <div className="flex items-center justify-center space-x-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage 
                      src={testimonials[currentTestimonial].avatar} 
                      alt={testimonials[currentTestimonial].name}
                    />
                    <AvatarFallback className="bg-blue-100 text-blue-600 text-lg font-semibold">
                      {testimonials[currentTestimonial].name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <div className="font-bold text-gray-900 text-lg">
                      {testimonials[currentTestimonial].name}
                    </div>
                    <div className="text-gray-600">
                      {testimonials[currentTestimonial].position}
                    </div>
                    <div className="text-blue-600 font-medium">
                      {testimonials[currentTestimonial].company}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Testimonial Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentTestimonial 
                    ? "bg-blue-600 scale-125" 
                    : "bg-gray-300 hover:bg-gray-400"
                }`}
                aria-label={`View testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* All Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card 
              key={testimonial.id}
              className={`transition-all duration-300 hover:shadow-lg ${
                index === currentTestimonial 
                  ? "ring-2 ring-blue-600 shadow-lg" 
                  : "hover:shadow-md"
              }`}
            >
              <CardContent className="p-6">
                {/* Rating */}
                <div className="flex mb-4">
                  {renderStars(testimonial.rating)}
                </div>

                {/* Content */}
                <blockquote className="text-gray-700 mb-6 leading-relaxed">
                  "{testimonial.content}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.position}</div>
                    <div className="text-sm text-blue-600 font-medium">{testimonial.company}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
